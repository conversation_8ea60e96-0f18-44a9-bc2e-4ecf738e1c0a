{"cli": {"version": ">= 16.0.0", "appVersionSource": "remote"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleDebug"}, "env": {"NODE_ENV": "development"}}, "preview": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}, "env": {"NODE_ENV": "production", "EAS_BUILD": "true"}}, "production": {"android": {"buildType": "app-bundle", "gradleCommand": ":app:bundleRelease"}, "env": {"NODE_ENV": "production", "EAS_BUILD": "true"}}}, "submit": {"production": {"android": {"serviceAccountKeyPath": "./google-service-account.json", "track": "internal"}}}}